{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Guardrails\n", "Guardrails are a way to validate the input and output of an Agent to insure proper usage."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from agents import Agent, GuardrailFunctionOutput, RunContextWrapper, Runner, TResponseInputItem, input_guardrail\n", "from pydantic import BaseModel\n", "\n", "class HomeworkCheatDetectionOutput(BaseModel):\n", "    attempting_cheat: bool\n", "    explanation: str\n", "\n", "homework_cheat_guardrail_agent = Agent(\n", "    name=\"Homework Cheat Detector\",\n", "    instructions=(\n", "        \"Determine if the user's query resembles a typical homework assignment or exam question, indicating an attempt to cheat. General questions about concepts are acceptable. \"\n", "        \" Cheating: 'Fill in the blank: The capital of France is ____.',\"\n", "        \" 'Which of the following best describes photosynthesis? A) Cellular respiration B) Conversion of light energy C) Evaporation D) Fermentation.'\"\n", "        \" Not-Cheating: 'What is the capital of France?', 'Explain photosynthesis.'\"\n", "    ),\n", "    output_type=HomeworkCheatDetectionOutput,\n", "    model=\"gpt-4o-mini\"\n", ")\n", "\n", "@input_guardrail\n", "async def cheat_detection_guardrail(\n", "        ctx: RunContextWrapper[None], agent: Agent, input: str | list[TResponseInputItem]\n", ") -> GuardrailFunctionOutput :\n", "    \n", "    detection_result = await Runner.run(homework_cheat_guardrail_agent, input)\n", "\n", "    return GuardrailFunctionOutput(\n", "        tripwire_triggered=detection_result.final_output.attempting_cheat,\n", "        output_info=detection_result.final_output\n", "    )\n", "\n", "study_helper_agent = Agent(\n", "    name=\"Study Helper Agent\",\n", "    instructions=\"You assist users in studying by explaining concepts or providing guidance, without directly solving homework or test questions.\",\n", "    model=\"gpt-4o\",\n", "    input_guardrails=[cheat_detection_guardrail]\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Homework cheat guardrail triggered\n", "Exception details: Guardrail InputGuardrail triggered tripwire\n"]}], "source": ["# This should trigger the cheat detection guardrail\n", "from agents import InputGuardrailTripwireTriggered\n", "\n", "\n", "try:\n", "    response = await <PERSON>.run(study_helper_agent, \"Fill in the blank: The process of converting light energy into chemical energy is called ____.\")\n", "    print(\"Guardrail didn't trigger\")\n", "    print(\"Response: \", response.final_output)\n", "\n", "except InputGuardrailTripwireTriggered as e:\n", "    print(\"Homework cheat guardrail triggered\")\n", "    print(\"Exception details:\", str(e))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Guardrail didn't trigger\n", "Response:  The main causes of the American Civil War include:\n", "\n", "1. **Slavery**: The primary and most contentious issue was slavery. The Southern economy relied heavily on slave labor for its agricultural operations, particularly in cotton production, while the North's economy was more industrialized and less dependent on slavery.\n", "\n", "2. **States' Rights**: There was a fundamental disagreement over states' rights versus federal authority. Southern states favored greater state sovereignty, especially when it came to deciding on the legality of slavery within their borders.\n", "\n", "3. **Economic Differences**: The North and South had distinct economic systems. The North was more industrialized, while the Southern economy was largely agricultural and reliant on exports. These differences led to conflicting interests, particularly regarding tariffs and trade policies.\n", "\n", "4. **Expansion and Sectionalism**: As the United States expanded westward, there was intense debate over whether new states should permit slavery. This fueled sectional tensions as both sides sought to maintain a balance of power in Congress.\n", "\n", "5. **Cultural Differences**: There were also significant cultural differences between the North and South, with distinct social norms, values, and lifestyles. These differences exacerbated misunderstandings and hostility.\n", "\n", "6. **Political Conflicts and Leadership**: Events such as the Missouri Compromise, Kansas-Nebraska Act, and the <PERSON><PERSON> decision heightened tensions. The election of <PERSON> in 1860, who was perceived as anti-slavery, was the final trigger for Southern secession.\n", "\n", "Each of these causes contributed to the deepening divide between North and South, ultimately leading to the outbreak of war in 1861.\n"]}], "source": ["# This should trigger the cheat detection guardrail\n", "try:\n", "    response = await <PERSON>.run(study_helper_agent, \"What were the main causes of the American civil war?\")\n", "    print(\"Guardrail didn't trigger\")\n", "    print(\"Response: \", response.final_output)\n", "\n", "except InputGuardrailTripwireTriggered as e:\n", "    print(\"Homework cheat guardrail triggered\")\n", "    print(\"Exception details:\", str(e))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "from agents import (\n", "    Agent,\n", "    GuardrailFunctionOutput,\n", "    OutputGuardrailTripwireTriggered,\n", "    RunContextWrapper,\n", "    Runner,\n", "    output_guardrail,\n", ")\n", "\n", "class MessageOutput(BaseModel):\n", "    response: str\n", "\n", "@output_guardrail\n", "async def forbidden_words_guardrail(ctx: RunContextWrap<PERSON>, agent: Agent, output: str) -> GuardrailFunctionOutput:\n", "    print(f\"Checking output for forbidden phrases: {output}\")\n", "\n", "    # Funny forbidden phrases to check\n", "    forbidden_phrases = [\"fart\", \"booger\", \"silly goose\"]\n", "\n", "    # Convert output to lowercase for case-insensitive comparison\n", "    output_lower = output.lower()\n", "\n", "    # Check which forbidden phrases are present in the response\n", "    found_phrases = [phrase for phrase in forbidden_phrases if phrase in output_lower]\n", "    trip_triggered = bool(found_phrases)\n", "\n", "    print(f\"Found forbidden phrases: {found_phrases}\")\n", "\n", "    return GuardrailFunctionOutput(\n", "        output_info={\n", "            \"reason\": \"Output contains forbidden phrases.\",\n", "            \"forbidden_phrases_found\": found_phrases,\n", "        },\n", "        tripwire_triggered=trip_triggered,\n", "    )\n", "\n", "agent = Agent(\n", "    name=\"Customer support agent\",\n", "    instructions=\"You are a customer support agent. You help customers with their questions.\",\n", "    output_guardrails=[forbidden_words_guardrail],\n", "    model=\"gpt-4o-mini\",\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking output for forbidden phrases: <PERSON><PERSON>! If you have any questions or need assistance, feel free to ask!\n", "Found forbidden phrases: ['fart']\n", "The agent said a bad word, he is fired.\n"]}], "source": ["try:\n", "    await <PERSON>.run(agent, \"Say the word fart\")\n", "    print(\"Guardrail didn't trip - this is unexpected\")\n", "except OutputGuardrailTripwireTriggered:\n", "    print(\"The agent said a bad word, he is fired.\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking output for forbidden phrases: Hello! I'm here to help you with any questions or concerns you might have. What can I assist you with today?\n", "Found forbidden phrases: []\n", "Guardrail didn't trip yay\n"]}], "source": ["try:\n", "    await <PERSON>.run(agent, \"Hey wassup\")\n", "    print(\"Guardrail didn't trip yay\")\n", "except OutputGuardrailTripwireTriggered:\n", "    print(\"The agent said a bad word, he is fired.\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}