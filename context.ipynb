{"cells": [{"cell_type": "markdown", "id": "3b4db3b2", "metadata": {}, "source": ["### Context\n", "A way to pass along data during the agent's lifecycle."]}, {"cell_type": "code", "execution_count": 4, "id": "8ff42aca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are now chatting with the shopping assistant. Type 'exit' to end the conversation.\n", "Getting account balance\n", "Shopping Assistant: Your budget is $100. What items would you like to include on your shopping list? Or would you like some suggestions?\n", "Shopping Assistant: Great choice! Here are some common ingredients for spaghetti:\n", "\n", "1. Spaghetti pasta\n", "2. Marinara or tomato sauce\n", "3. Ground beef or turkey\n", "4. <PERSON><PERSON><PERSON>\n", "5. <PERSON><PERSON>\n", "6. Olive oil\n", "7. Parmesan cheese\n", "8. Salt and pepper\n", "9. Basil or oregano (optional)\n", "\n", "Would you like me to add these to your shopping cart or do you have specific items in mind?\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Searching for item\n", "Shopping Assistant: The total cost of your items exceeds your $100 budget. Here are the prices:\n", "\n", "- Spaghetti pasta: $85.00\n", "- Marinara sauce: $30.00\n", "- Ground beef: $73.00\n", "- Garlic: $59.00\n", "- Onion: $99.00\n", "- Olive oil: $14.00\n", "- Parmesan cheese: $14.00\n", "- Salt: $63.00\n", "- Pepper: $5.00\n", "- Basil: $46.00\n", "\n", "Would you like some suggestions to adjust this list?\n", "Shopping Assistant: Let's focus on essentials for a simple yet delicious spaghetti dish and make adjustments:\n", "\n", "1. **Spaghetti pasta**: $85.00\n", "2. **Marinara sauce**: $30.00\n", "3. **Ground beef**: $73.00\n", "4. **<PERSON><PERSON><PERSON>**: $59.00\n", "5. **Onion**: $99.00\n", "6. **Olive oil**: $14.00\n", "7. **Parmesan cheese**: $14.00\n", "8. **Salt**: $63.00\n", "9. **Pepper**: $5.00\n", "10. **Basil**: $46.00\n", "\n", "Here’s a more budget-friendly list:\n", "\n", "1. Spaghetti pasta: $85.00\n", "2. Marinara sauce: $30.00\n", "3. Parmesan cheese: $14.00\n", "4. Pepper: $5.00\n", "\n", "These total $134.00, still a bit over budget. Would you like to remove any items or substitute with lower-cost options?\n", "Shopping Assistant: Sounds like quite the unique meal! Would you like me to add just the pepper to your cart, or is there anything else you might need?\n", "Shopping Assistant: No problem! Let's review the essentials for your spaghetti dinner within your budget:\n", "\n", "1. **Spaghetti pasta**\n", "2. **Marinara sauce**\n", "3. **Parmesan cheese**\n", "\n", "Considering the prices, we may need to adjust some of these or opt for smaller quantities. Would you like to proceed with these adjustments or explore any further substitutions?\n", "Adding items to shopping cart\n", "Purchasing items\n", "Successfully purchased items: ['Pepper']\n", "Shopping Assistant: The pepper has been added to your cart and purchased. If there's anything else you need, feel free to ask!\n", "Goodbye!\n"]}], "source": ["from dataclasses import dataclass\n", "import random\n", "\n", "from agents import Agent, RunContextWrapper, Runner, TResponseInputItem, function_tool\n", "\n", "\n", "@dataclass\n", "class UserProfile:\n", "    id: str\n", "    name: str\n", "    shopping_cart: list[str]\n", "\n", "@function_tool\n", "async def get_budget(wrapper: RunContextWrapper[UserProfile]):\n", "    \"\"\"\n", "    Get the account balance of the user using the user's id and their linked bank account\n", "    \"\"\"\n", "    print(\"Getting account balance\")\n", "    user_id = wrapper.context.id\n", "\n", "    # pretend we are fetching the account balance from a database\n", "\n", "    return 100.0\n", "\n", "@function_tool\n", "async def search_for_item(wrapper: RunContextWrapper[UserProfile], item: str) -> str:\n", "    \"\"\"\n", "    Search for an item in the database\n", "    \"\"\"\n", "    print(\"Searching for item\")\n", "    # randomly generate a price for the item\n", "    price = random.randint(1, 100)\n", "    return f\"Found {item} in the database for ${price}.00\"\n", "\n", "@function_tool\n", "async def get_shopping_cart(wrapper: RunContextWrapper[UserProfile]) -> list[str]:\n", "    print(\"Getting shopping cart\")\n", "    return wrapper.context.shopping_cart\n", "\n", "@function_tool\n", "async def add_to_shopping_cart(wrapper: RunContextWrapper[UserProfile], items: list[str]) -> None:\n", "    print(\"Adding items to shopping cart\")\n", "    wrapper.context.shopping_cart.extend(items)\n", "    \n", "@function_tool\n", "async def purchase_items(wrapper: RunContextWrapper[UserProfile]) -> None:\n", "    print(\"Purchasing items\")\n", "    \n", "    # we could take the items from the shopping cart and purchase them using some external API\n", "    # for now, we'll just print a message\n", "    print(f\"Successfully purchased items: {wrapper.context.shopping_cart}\")\n", "\n", "shopping_agent = Agent[UserProfile](\n", "    name=\"Shopping Assistant\",\n", "    instructions=(\n", "        \"You are a shopping assistant dedicated to helping the user with their grocery shopping needs.\"\n", "        \"Your primary role is to assist in creating a shopping plan that fits within the user's budget.\"\n", "        \"Start by getting the user's budget using the tool get_budget.\"\n", "        \"Provide suggestions for items if requested, and always aim to keep the total cost within the user's budget.\"\n", "        \"If the user is nearing or exceeding their budget, inform them and suggest alternatives or adjustments to the shopping list.\"\n", "        \"If the user authorizes it, you can purchase the items using the tool purchase_items.\"\n", "    ),\n", "    tools=[get_shopping_cart, add_to_shopping_cart, get_budget, search_for_item, purchase_items],\n", ")\n", "\n", "profile = UserProfile(id=\"123\", name=\"<PERSON>\", shopping_cart=[])\n", "print(\"You are now chatting with the shopping assistant. Type 'exit' to end the conversation.\")\n", "convo_items: list[TResponseInputItem] = []\n", "while True:\n", "    user_input = input(\"You: \")\n", "\n", "    if user_input == \"exit\":\n", "        print(\"Goodbye!\")\n", "        break\n", "\n", "    convo_items.append({\"content\": user_input, \"role\": \"user\"})\n", "    result = await Runner.run(shopping_agent, convo_items, context=profile)\n", "    \n", "    print(f\"Shopping Assistant: {result.final_output}\")\n", "    \n", "    convo_items = result.to_input_list()"]}, {"cell_type": "code", "execution_count": 6, "id": "d7fcedaa", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "from agents import (\n", "    Agent,\n", "    GuardrailFunctionOutput,\n", "    InputGuardrailTripwireTriggered,\n", "    RunContextWrapper,\n", "    Runner,\n", "    TResponseInputItem,\n", "    input_guardrail,\n", ")\n", "\n", "class UserProfile(BaseModel):\n", "    id: str\n", "    name: str\n", "    admin: bool\n", "\n", "class HomeworkCheatDetectionOutput(BaseModel):\n", "    attempting_cheat: bool\n", "    explanation: str\n", "\n", "homework_cheat_guardrail_agent = Agent(\n", "    name=\"Homework Cheat Detector\",\n", "    instructions=(\n", "        \"Determine if the user's query resembles a typical homework assignment or exam question, indicating an attempt to cheat. General questions about concepts are acceptable. \"\n", "        \" Cheating: 'Fill in the blank: The capital of France is ____.',\"\n", "        \" 'Which of the following best describes photosynthesis? A) Cellular respiration B) Conversion of light energy C) Evaporation D) Fermentation.'\"\n", "        \" Not-Cheating: 'What is the capital of France?', 'Explain photosynthesis.'\"\n", "    ),\n", "    output_type=HomeworkCheatDetectionOutput,\n", "    model=\"gpt-4o-mini\" # usually the guardrail agent can be cheaper than the main agent\n", ")\n", "\n", "@input_guardrail\n", "async def cheat_detection_guardrail(\n", "    ctx: RunContextWrapper[UserProfile], agent: Agent, input: str | list[TResponseInputItem]\n", ") -> GuardrailFunctionOutput:\n", "    # Skip guardrail check if user is admin\n", "    if ctx.context.admin:\n", "        return GuardrailFunctionOutput(\n", "            output_info=HomeworkCheatDetectionOutput(attempting_cheat=False, explanation=\"Admin bypass\"),\n", "            tripwire_triggered=False\n", "        )\n", "\n", "    detection_result = await Runner.run(homework_cheat_guardrail_agent, input, context=ctx.context)\n", "\n", "    return GuardrailFunctionOutput(\n", "        output_info=detection_result.final_output,\n", "        tripwire_triggered=detection_result.final_output.attempting_cheat,\n", "    )\n", "\n", "study_helper_agent = Agent[UserProfile](\n", "    name=\"Study Helper Agent\",\n", "    instructions=\"You assist users in studying by explaining concepts or providing guidance, without directly solving homework or test questions.\",\n", "    input_guardrails=[cheat_detection_guardrail],\n", "    model=\"gpt-4o\"\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "38a2a824", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Homework cheat guardrail triggered\n", "Exception details: Guardrail InputGuardrail triggered tripwire\n"]}], "source": ["# This should NOT trigger the cheat detection guardrail since the user is an admin\n", "try:\n", "    response = await Runner.run(study_helper_agent, \n", "                                \"Fill in the blank: The capital of France is ____.\", \n", "                                context=UserProfile(id=\"123\", name=\"<PERSON>\", admin=False))\n", "    print(\"Guardrail didn't trigger\")\n", "    print(\"Response: \", response.final_output)\n", "\n", "except InputGuardrailTripwireTriggered as e:\n", "    print(\"Homework cheat guardrail triggered\")\n", "    print(\"Exception details:\", str(e))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}