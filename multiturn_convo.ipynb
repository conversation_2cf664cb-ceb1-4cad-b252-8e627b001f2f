{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'content': 'Hey!', 'role': 'user'}\n", "{'content': 'Hey! How are you? How can I help you today?', 'role': 'assistant'}\n", "{'content': 'Im having a bad day', 'role': 'user'}\n", "{'id': 'msg_67e868b45444819181be3be2e97916920b5e95bb26736f2a', 'content': [{'annotations': [], 'text': \"I'm sorry to hear that. Do you want to talk about what's going on?\", 'type': 'output_text'}], 'role': 'assistant', 'status': 'completed', 'type': 'message'}\n"]}], "source": ["from agents import Agent, Runner, TResponseInputItem\n", "\n", "simple_agent = Agent(\n", "    name=\"<PERSON>\",\n", "    instructions=\"You are a friendly assistant. Reply concisely.\"\n", ")\n", "\n", "convo: list[TResponseInputItem] = [\n", "    {\"content\": \"Hey!\", \"role\": \"user\"},\n", "    {\"content\": \"Hey! How are you? How can I help you today?\", \"role\": \"assistant\"},\n", "    {\"content\": \"Im having a bad day\", \"role\": \"user\"}\n", "]\n", "result = await Runner.run(simple_agent, convo)\n", "for message in result.to_input_list():\n", "    print(message)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are now chatting with agent <PERSON>. Type 'exit' to end the conversation.\n", "You: hey\n", "Agent: Hey there! How can I assist you today?\n", "You: exit\n", "Goodbye!\n"]}], "source": ["convo: list[TResponseInputItem] = []\n", "print(\"You are now chatting with agent <PERSON>. Type 'exit' to end the conversation.\")\n", "while True:\n", "    user_input = input(\"You: \")\n", "    print(\"You: \" + user_input)\n", "\n", "    if user_input == \"exit\":\n", "        print(\"Goodbye!\")\n", "        break\n", "\n", "    convo.append({\"content\": user_input, \"role\": \"user\"})\n", "    result = await Runner.run(simple_agent, convo)\n", "\n", "    print(f\"Agent: {result.final_output}\")\n", "\n", "    convo = result.to_input_list()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are now chatting with the triage agent. Type 'exit' to end the conversation.\n", "You: hey\n", "Triage Agent: Hello! How can I assist you today? Do you have a homework question or something else on your mind?\n", "\n", "You: help me learn how to divide\n", "Handing off to math tutor agent\n", "Math Tutor: Of course! Let’s go through the basics of division and look at some examples.\n", "\n", "### What is Division?\n", "\n", "Division is the process of splitting a number (called the dividend) into equal parts. The number you divide by is called the divisor, and the result is the quotient. Sometimes, there may be a remainder.\n", "\n", "### Example 1: Simple Division\n", "\n", "Let's divide 20 by 4.\n", "\n", "1. **Dividend**: 20\n", "2. **Divisor**: 4\n", "\n", "To find the **Quotient**, think of how many times 4 fits into 20 exactly. \n", "\n", "\\[ 4 \\times 5 = 20 \\]\n", "\n", "So, 20 divided by 4 equals 5. Here, the quotient is 5, and there is no remainder.\n", "\n", "### Example 2: Division with a <PERSON><PERSON><PERSON>\n", "\n", "Now, let’s divide 22 by 4.\n", "\n", "1. **Dividend**: 22\n", "2. **Divisor**: 4\n", "\n", "Check how many times 4 fits into 22 without exceeding it.\n", "\n", "\\[ 4 \\times 5 = 20 \\]\n", "\n", "Subtract 20 from 22, and you’re left with 2.\n", "\n", "So, 22 divided by 4 equals 5 with a remainder of 2.\n", "\n", "### Steps for Division\n", "\n", "1. **Divide**: How many times does the divisor go into the dividend?\n", "2. **Multiply**: Multiply the divisor by the quotient.\n", "3. **Subtract**: Subtract the result from the dividend to find the remainder.\n", "4. **Bring down** (if you are working with larger numbers or decimals).\n", "\n", "### Example 3: Long Division\n", "\n", "Divide 135 by 6.\n", "\n", "1. **Divide**: 6 goes into 13 (from 135) twice.\n", "   - **Quotient**: 2\n", "2. **Multiply**: \\( 2 \\times 6 = 12 \\)\n", "3. **Subtract**: \\( 13 - 12 = 1 \\)\n", "4. **Bring down**: Bring down the next digit, making it 15.\n", "5. **Repeat**: 6 goes into 15 twice.\n", "   - **Quotient**: 2\n", "6. **Multiply**: \\( 2 \\times 6 = 12 \\)\n", "7. **Subtract**: \\( 15 - 12 = 3 \\)\n", "\n", "So, 135 divided by 6 equals 22 with a remainder of 3.\n", "\n", "Feel free to ask further questions or request more examples!\n", "\n", "You: thanks\n", "Math Tutor: You're welcome! If you need more help or examples, feel free to ask anytime. Happy studying!\n", "\n", "You: exit\n", "Goodbye!\n"]}], "source": ["from agents import Agent, Runner, handoff, RunContextWrapper\n", "\n", "history_tutor_agent = Agent(\n", "    name=\"<PERSON> Tutor\",\n", "    handoff_description=\"Specialist agent for historical questions\",\n", "    instructions=\"You provide assistance with historical queries. Explain important events and context clearly.\",\n", ")\n", "\n", "math_tutor_agent = Agent(\n", "    name=\"<PERSON>\",\n", "    handoff_description=\"Specialist agent for math questions\",\n", "    instructions=\"You provide assistance with math queries. Explain your reasoning at each step and include examples\"\n", ")\n", "\n", "def on_math_handoff(ctx: RunContextWrapper[None]):\n", "    print(\"Handing off to math tutor agent\")\n", "\n", "# This agent has the capability to handoff to either the history or math tutor agent\n", "triage_agent = Agent(\n", "    name=\"Triage Agent\",\n", "    instructions=\"You determine which agent to use based on the user's homework question.\" +\n", "    \"If neither agent is relevant, provide a general response.\",\n", "    handoffs=[history_tutor_agent, handoff(math_tutor_agent, on_handoff=on_math_handoff)]\n", ")\n", "\n", "convo: list[TResponseInputItem] = []\n", "last_agent = triage_agent\n", "print(\"You are now chatting with the triage agent. Type 'exit' to end the conversation.\")\n", "while True:\n", "    user_input = input(\"You: \")\n", "    print(\"You: \" + user_input)\n", "\n", "    if user_input == \"exit\":\n", "        print(\"Goodbye!\")\n", "        break\n", "\n", "    convo.append({\"content\": user_input, \"role\": \"user\"})\n", "    result = await Runner.run(last_agent, convo)\n", "\n", "    convo = result.to_input_list()\n", "    last_agent = result.last_agent\n", "\n", "    print(f\"{last_agent.name}: {result.final_output}\\n\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}