{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "# loads the .env file (if you have a global environment variable, you can skip this)\n", "load_dotenv()\n", "\n", "api_key = os.environ.get(\"OPENAI_API_KEY\")\n", "\n", "if not api_key:\n", "    raise ValueError(\"OPENAI_API_KEY is not set in the environment variables\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tracing\n", "A way to see what the agents are doing."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"HELLO! I'M JUST A PROGRAM, BUT I'M HERE AND READY TO HELP YOU! HOW CAN I ASSIST YOU TODAY?\""]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from agents import Agent, Runner\n", "\n", "agent = Agent(\n", "    name=\"Basic Agent\",\n", "    instructions=\"You are a helpful assistant. Respond on in all caps.\",\n", "    model=\"gpt-4o-mini\"\n", ")\n", "\n", "# when doing a single run the tracing is automatically done\n", "result = await <PERSON>.run(agent, \"Hello! How are you?\")\n", "result.final_output"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Translated joke:\n", "¿Por qué el queso fue solo a la fiesta?\n", "\n", "¡Porque se sentía azul!\n"]}], "source": ["from agents import trace\n", "\n", "joke_agent = Agent(\n", "    name=\"Joke Agent\",\n", "    instructions=\"You are a joke teller. You are given a topic and you need to tell a joke about it.\",\n", ")\n", "language_agent = Agent(\n", "    name=\"Language Agent\",\n", "    instructions=\"You are a language expert. You are given a joke and you need to rewrite it in a different language.\",\n", ")\n", "\n", "with trace(\"Joke Translation Workflow\"):\n", "    joke_result = await Runner.run(joke_agent, \"Cheese\")\n", "    translated_result = await Runner.run(language_agent, f\"Translate this joke to Spanish: {joke_result.final_output}\")\n", "    print(f\"Translated joke:\\n{translated_result.final_output}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Escalating to manager agent:  The refund page is blank despite clearing cache and using a different browser.\n", "Reason for escalation:  The issue persists across different browsers and after clearing cache, indicating a potential backend or server issue that requires managerial attention.\n", "Creating ticket for issue: Customer is experiencing a blank page when attempting to refund an order. The issue persists across different browsers and after clearing cache.\n", "I've created a support ticket to address this issue (Ticket ID: 12345). Our technical team will investigate and resolve it as soon as possible. Thank you for your patience!\n"]}], "source": ["from agents import function_tool, RunContextWrapper, handoff\n", "from pydantic import BaseModel\n", "\n", "class ManagerEscalation(BaseModel):\n", "    issue: str # the issue being escalated\n", "    why: str # why can you not handle it? Used for training in the future\n", "\n", "@function_tool\n", "def create_ticket(issue: str):\n", "    \"\"\"\"\n", "    Create a ticket in the system for an issue to be resolved.\n", "    \"\"\"\n", "    print(f\"Creating ticket for issue: {issue}\")\n", "    return \"Ticket created. ID: 12345\"\n", "    # In a real-world scenario, this would interact with a ticketing system\n", "\n", "manager_agent = Agent(\n", "    name=\"Manager\",\n", "    handoff_description=\"Handles escalated issues that require managerial attention\",\n", "    instructions=(\n", "        \"You handle escalated customer issues that the initial custom service agent could not resolve. \"\n", "        \"You will receive the issue and the reason for escalation. If the issue cannot be immediately resolved for the \"\n", "        \"customer, create a ticket in the system and inform the customer.\"\n", "    ),\n", "    tools=[create_ticket],\n", ")\n", "\n", "def on_manager_handoff(ctx: RunContextWrapper[None], input: ManagerEscalation):\n", "    print(\"Escalating to manager agent: \", input.issue)\n", "    print(\"Reason for escalation: \", input.why)\n", "\n", "    # here we might store the escalation in a database or log it for future reference\n", "\n", "customer_service_agent = Agent(\n", "    name=\"Customer Service\",\n", "    instructions=\"You assist customers with general inquiries and basic troubleshooting. \" +\n", "                 \"If the issue cannot be resolved, escalate it to the Manager along with the reason why you cannot fix the issue yourself.\",\n", "    handoffs=[handoff(\n", "        agent=manager_agent,\n", "        input_type=ManagerEscalation,\n", "        on_handoff=on_manager_handoff,\n", "    )]\n", ")\n", "\n", "with trace(\"Customer Service Hotline\"):\n", "    result = await Runner.run(customer_service_agent, \"Hello im having an issue with refunding my order. The page is blank and I cannot see anything. I have tried clearing my cache and using a different browser. I am not sure what else to do.\")\n", "    print(result.final_output)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}