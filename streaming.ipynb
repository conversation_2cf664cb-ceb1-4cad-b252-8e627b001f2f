{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sure, here are five jokes for you:\n", "\n", "1. Why don't skeletons fight each other?\n", "   - They don't have the guts.\n", "\n", "2. What do you call fake spaghetti?\n", "   - An impasta!\n", "\n", "3. Why did the scarecrow win an award?\n", "   - Because he was outstanding in his field!\n", "\n", "4. What do you call a dinosaur with an extensive vocabulary?\n", "   - A thesaurus.\n", "\n", "5. Why can't you give <PERSON> a balloon?\n", "   - Because she will let it go!"]}], "source": ["from openai.types.responses import ResponseTextDeltaEvent\n", "\n", "from agents import Agent, Runner\n", "\n", "agent = Agent(\n", "    name=\"<PERSON>\",\n", "    instructions=\"You are a helpful assistant.\",\n", ")\n", "\n", "result = Runner.run_streamed(agent, input=\"Please tell me 5 jokes.\")\n", "async for event in result.stream_events():\n", "    if event.type == \"raw_response_event\" and isinstance(event.data, ResponseTextDeltaEvent):\n", "        print(event.data.delta, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Run starting ===\n", "Agent updated: <PERSON>\n", "-- <PERSON><PERSON> was called\n", "-- Tool output: 6\n", "-- Message output:\n", " Here are six jokes for you:\n", "\n", "1. **Why don't scientists trust atoms?**\n", "   Because they make up everything!\n", "\n", "2. **Did you hear about the mathematician who’s afraid of negative numbers?**\n", "   He'll stop at nothing to avoid them.\n", "\n", "3. **Why was the math book sad?**\n", "   Because it had too many problems.\n", "\n", "4. **What do you call fake spaghetti?**\n", "   An impasta!\n", "\n", "5. **Why did the scarecrow win an award?**\n", "   Because he was outstanding in his field!\n", "\n", "6. **Have you heard about the restaurant on the moon?**\n", "   Great food, no atmosphere.\n", "=== Run complete ===\n"]}], "source": ["import random\n", "from agents import Agent, ItemHelpers, Runner, function_tool\n", "\n", "@function_tool\n", "def how_many_jokes() -> int:\n", "    return random.ran<PERSON><PERSON>(1, 10)\n", "\n", "agent = Agent(\n", "    name=\"<PERSON>\",\n", "    instructions=\"First call the `how_many_jokes` tool, then tell that many jokes.\",\n", "    tools=[how_many_jokes],\n", ")\n", "\n", "result = Runner.run_streamed(\n", "    agent,\n", "    input=\"Hello\",\n", ")\n", "print(\"=== Run starting ===\")\n", "\n", "async for event in result.stream_events():\n", "    # We'll ignore the raw responses event deltas\n", "    if event.type == \"raw_response_event\":\n", "        continue\n", "    # When the agent updates, print that\n", "    elif event.type == \"agent_updated_stream_event\":\n", "        print(f\"Agent updated: {event.new_agent.name}\")\n", "        continue\n", "    # When items are generated, print them\n", "    elif event.type == \"run_item_stream_event\":\n", "        if event.item.type == \"tool_call_item\":\n", "            print(\"-- <PERSON><PERSON> was called\")\n", "        elif event.item.type == \"tool_call_output_item\":\n", "            print(f\"-- Tool output: {event.item.output}\")\n", "        elif event.item.type == \"message_output_item\":\n", "            print(f\"-- Message output:\\n {ItemHelpers.text_message_output(event.item)}\")\n", "        else:\n", "            pass  # Ignore other event types\n", "\n", "print(\"=== Run complete ===\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}