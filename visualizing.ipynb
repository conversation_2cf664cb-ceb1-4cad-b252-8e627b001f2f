{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.0 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["%pip install \"openai-agents[viz]\" -qU"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# to install graphviz: https://www.graphviz.org/download"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from agents import Agent, Runner, function_tool, handoff, RunContextWrapper\n", "from agents.extensions.visualization import draw_graph\n", "\n", "@function_tool\n", "def print_something():\n", "    print(\"blah blah\")\n", "\n", "history_tutor_agent = Agent(\n", "    name=\"<PERSON> Tutor\",\n", "    handoff_description=\"Specialist agent for historical questions\",\n", "    instructions=\"You provide assistance with historical queries. Explain important events and context clearly.\",\n", ")\n", "\n", "math_tutor_agent = Agent(\n", "    name=\"<PERSON>\",\n", "    handoff_description=\"Specialist agent for math questions\",\n", "    instructions=\"You provide assistance with math queries. Explain your reasoning at each step and include examples\"\n", ")\n", "\n", "def on_math_handoff(ctx: RunContextWrapper[None]):\n", "    print(\"Handing off to math tutor agent\")\n", "\n", "def on_history_handoff(ctx: RunContextWrapper[None]):\n", "    print(\"Handing off to history tutor agent\")\n", "\n", "# This agent has the capability to handoff to either the history or math tutor agent\n", "triage_agent = Agent(\n", "    name=\"Triage Agent\",\n", "    instructions=\"You determine which agent to use based on the user's homework question.\" +\n", "    \"If neither agent is relevant, provide a general response.\",\n", "    handoffs=[handoff(history_tutor_agent, on_handoff=on_history_handoff), \n", "              handoff(math_tutor_agent, on_handoff=on_math_handoff)],\n", "    tools=[print_something]\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 12.2.1 (20241206.2353)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"420pt\" height=\"229pt\"\n", " viewBox=\"0.00 0.00 419.73 228.79\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 224.79)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"none\" points=\"-4,4 -4,-224.79 415.73,-224.79 415.73,4 -4,4\"/>\n", "<!-- __start__ -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>__start__</title>\n", "<ellipse fill=\"lightblue\" stroke=\"black\" cx=\"155.73\" cy=\"-203.99\" rx=\"51.09\" ry=\"16.79\"/>\n", "<text text-anchor=\"middle\" x=\"155.73\" y=\"-198.57\" font-family=\"Arial\" font-size=\"14.00\">__start__</text>\n", "</g>\n", "<!-- Triage Agent -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>Triage Agent</title>\n", "<polygon fill=\"lightyellow\" stroke=\"black\" points=\"209.73,-151.2 101.73,-151.2 101.73,-93.6 209.73,-93.6 209.73,-151.2\"/>\n", "<text text-anchor=\"middle\" x=\"155.73\" y=\"-116.97\" font-family=\"Arial\" font-size=\"14.00\">Triage Agent</text>\n", "</g>\n", "<!-- __start__&#45;&gt;Triage Agent -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>__start__&#45;&gt;Triage Agent</title>\n", "<path fill=\"none\" stroke=\"black\" stroke-width=\"1.5\" d=\"M155.73,-186.89C155.73,-180.05 155.73,-171.75 155.73,-163.43\"/>\n", "<polygon fill=\"black\" stroke=\"black\" stroke-width=\"1.5\" points=\"159.23,-163.68 155.73,-153.68 152.23,-163.68 159.23,-163.68\"/>\n", "</g>\n", "<!-- __end__ -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>__end__</title>\n", "<ellipse fill=\"lightblue\" stroke=\"black\" cx=\"273.73\" cy=\"-203.99\" rx=\"48.44\" ry=\"16.79\"/>\n", "<text text-anchor=\"middle\" x=\"273.73\" y=\"-198.57\" font-family=\"Arial\" font-size=\"14.00\">__end__</text>\n", "</g>\n", "<!-- print_something -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>print_something</title>\n", "<ellipse fill=\"lightgreen\" stroke=\"black\" cx=\"79.73\" cy=\"-28.8\" rx=\"79.73\" ry=\"16.79\"/>\n", "<text text-anchor=\"middle\" x=\"79.73\" y=\"-23.37\" font-family=\"Arial\" font-size=\"14.00\">print_something</text>\n", "</g>\n", "<!-- Triage Agent&#45;&gt;print_something -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>Triage Agent&#45;&gt;print_something</title>\n", "<path fill=\"none\" stroke=\"black\" stroke-width=\"1.5\" stroke-dasharray=\"1,5\" d=\"M126.24,-93.42C115.57,-81.37 104,-67.38 94.98,-55.59\"/>\n", "<polygon fill=\"black\" stroke=\"black\" stroke-width=\"1.5\" points=\"97.92,-53.67 89.15,-47.72 92.3,-57.84 97.92,-53.67\"/>\n", "</g>\n", "<!-- History Tutor -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>History Tutor</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M273.73,-57.6C273.73,-57.6 189.73,-57.6 189.73,-57.6 183.73,-57.6 177.73,-51.6 177.73,-45.6 177.73,-45.6 177.73,-12 177.73,-12 177.73,-6 183.73,0 189.73,0 189.73,0 273.73,0 273.73,0 279.73,0 285.73,-6 285.73,-12 285.73,-12 285.73,-45.6 285.73,-45.6 285.73,-51.6 279.73,-57.6 273.73,-57.6\"/>\n", "<text text-anchor=\"middle\" x=\"231.73\" y=\"-23.37\" font-family=\"Arial\" font-size=\"14.00\">History Tutor</text>\n", "</g>\n", "<!-- Triage Agent&#45;&gt;History Tutor -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>Triage Agent&#45;&gt;History Tutor</title>\n", "<path fill=\"none\" stroke=\"black\" stroke-width=\"1.5\" d=\"M178.94,-93.42C185.8,-85.15 193.42,-75.96 200.69,-67.2\"/>\n", "<polygon fill=\"black\" stroke=\"black\" stroke-width=\"1.5\" points=\"203.25,-69.6 206.94,-59.67 197.86,-65.13 203.25,-69.6\"/>\n", "</g>\n", "<!-- <PERSON> -->\n", "<g id=\"node6\" class=\"node\">\n", "<title><PERSON></title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M399.73,-57.6C399.73,-57.6 315.73,-57.6 315.73,-57.6 309.73,-57.6 303.73,-51.6 303.73,-45.6 303.73,-45.6 303.73,-12 303.73,-12 303.73,-6 309.73,0 315.73,0 315.73,0 399.73,0 399.73,0 405.73,0 411.73,-6 411.73,-12 411.73,-12 411.73,-45.6 411.73,-45.6 411.73,-51.6 405.73,-57.6 399.73,-57.6\"/>\n", "<text text-anchor=\"middle\" x=\"357.73\" y=\"-23.37\" font-family=\"Arial\" font-size=\"14.00\"><PERSON> Tu<PERSON></text>\n", "</g>\n", "<!-- Triage Agent&#45;&gt;Math Tutor -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>Triage Agent&#45;&gt;Math Tutor</title>\n", "<path fill=\"none\" stroke=\"black\" stroke-width=\"1.5\" d=\"M210.12,-96.73C235.38,-85.28 265.61,-71.57 292.12,-59.55\"/>\n", "<polygon fill=\"black\" stroke=\"black\" stroke-width=\"1.5\" points=\"293.5,-62.77 301.16,-55.45 290.61,-56.39 293.5,-62.77\"/>\n", "</g>\n", "<!-- print_something&#45;&gt;Triage Agent -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>print_something&#45;&gt;Triage Agent</title>\n", "<path fill=\"none\" stroke=\"black\" stroke-width=\"1.5\" stroke-dasharray=\"1,5\" d=\"M97.87,-45.37C107.91,-55.94 120.38,-70.39 131.23,-83.86\"/>\n", "<polygon fill=\"black\" stroke=\"black\" stroke-width=\"1.5\" points=\"128.43,-85.96 137.38,-91.64 133.92,-81.62 128.43,-85.96\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x23b7a027980>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["draw_graph(triage_agent)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}