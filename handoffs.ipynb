{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Handoffs\n", "A way for an agent to invoke another agent"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()\n", "\n", "api_key = os.environ.get(\"OPENAI_API_KEY\")\n", "if not api_key:\n", "    raise ValueError(\"OPENAI_API_KEY is not set in the environment variables\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["outline='1. Introduction to Loops in Java\\n2. The `for` Loop\\n   - Syntax\\n   - Example: Counting from 1 to 5\\n3. The `while` Loop\\n   - Syntax\\n   - Example: Countdown from 5\\n4. The `do-while` Loop\\n   - Syntax\\n   - Example: Print numbers until reaching 5\\n5. Enhanced `for` Loop (for-each)\\n   - Syntax\\n   - Example: Iterating over an array\\n6. Nested Loops\\n   - Example: Multiplication table\\n7. Best Practices\\n   - Avoiding infinite loops\\n   - Loop control statements' tutorial='# Loops in Java\\n\\nLoops are a fundamental part of Java programming that allow you to execute a block of code multiple times. Understanding loops will help you handle tasks that require repetition efficiently.\\n\\n## 1. The `for` Loop\\n\\n### Syntax\\n\\nThe `for` loop is used for iterating over a range. It consists of three parts:\\n\\n- Initialization\\n- Condition\\n- Update\\n\\n```java\\nfor(initialization; condition; update) {\\n    // Code to be executed\\n}\\n```\\n\\n### Example: Counting from 1 to 5\\n\\n```java\\nfor(int i = 1; i <= 5; i++) {\\n    System.out.println(i);\\n}\\n// This will print numbers 1 to 5\\n```\\n\\n## 2. The `while` Loop\\n\\n### Syntax\\n\\nThe `while` loop executes a block of code as long as the condition is true.\\n\\n```java\\nwhile(condition) {\\n    // Code to be executed\\n}\\n```\\n\\n### Example: Countdown from 5\\n\\n```java\\nint count = 5;\\nwhile(count > 0) {\\n    System.out.println(count);\\n    count--;\\n}\\n// This will print numbers 5 to 1\\n```\\n\\n## 3. The `do-while` Loop\\n\\n### Syntax\\n\\nThe `do-while` loop is similar to the `while` loop, but it checks the condition after executing the block of code. This guarantees that the code will run at least once.\\n\\n```java\\ndo {\\n    // Code to be executed\\n} while(condition);\\n```\\n\\n### Example: Print numbers until reaching 5\\n\\n```java\\nint number = 1;\\ndo {\\n    System.out.println(number);\\n    number++;\\n} while(number <= 5);\\n// This will print numbers 1 to 5\\n```\\n\\n## 4. Enhanced `for` Loop (for-each)\\n\\n### Syntax\\n\\nThe enhanced `for` loop is used for iterating over arrays or collections.\\n\\n```java\\nfor(type element : array) {\\n    // Code to be executed\\n}\\n```\\n\\n### Example: Iterating over an array\\n\\n```java\\nint[] numbers = {1, 2, 3, 4, 5};\\nfor(int number : numbers) {\\n    System.out.println(number);\\n}\\n// This will print each number in the array\\n```\\n\\n## 5. Nested Loops\\n\\nNested loops are loops inside another loop.\\n\\n### Example: Multiplication table\\n\\n```java\\nfor(int i = 1; i <= 5; i++) {\\n    for(int j = 1; j <= 5; j++) {\\n        System.out.print(i * j + \" \");\\n    }\\n    System.out.println();\\n}\\n// This will print a 5x5 multiplication table\\n```\\n\\n## 6. Best Practices\\n\\n### Avoiding Infinite Loops\\n\\nAlways ensure that the loop has a condition that can terminate. Infinite loops can crash your program.\\n\\n### Loop Control Statements\\n\\nUse `break` and `continue` to control loop execution.\\n\\n- `break` will exit the loop.\\n- `continue` will skip the current iteration.\\n\\n```java\\nfor(int i = 1; i <= 10; i++) {\\n    if(i == 5) {\\n        continue; // Skip the number 5\\n    }\\n    System.out.println(i);\\n}\\n\\nfor(int i = 1; i <= 10; i++) {\\n    if(i == 7) {\\n        break; // Stop the loop when 7 is reached\\n    }\\n    System.out.println(i);\\n}\\n```\\n'\n"]}], "source": ["from agents import Agent, Runner\n", "from pydantic import BaseModel\n", "\n", "class Tutorial(BaseModel):\n", "    outline: str\n", "    tutorial: str\n", "\n", "tutorial_generator = Agent(\n", "    name=\"Tutorial Generator\",\n", "    handoff_description=\"Used for generating a tutorial based on an outline.\",\n", "    instructions=(\n", "        \"Given a programming topic and an outline, your job is to generate code snippets for each section of the outline.\"\n", "        \"Format the tutorial in Markdown using a mix of text for explanation and code snippets for examples.\"\n", "        \"Where it makes sense, include comments in the code snippets to further explain the code.\"\n", "    ),\n", "    output_type=Tutorial\n", ")\n", "\n", "outline_builder = Agent(\n", "    name=\"Outline Builder\",\n", "    instructions=(\n", "        \"Given a particular programming topic, your job is to help come up with a tutorial. You will do that by crafting an outline.\"\n", "        \"After making the outline, hand it to the tutorial generator agent.\"\n", "    ),\n", "    handoffs=[tutorial_generator]\n", ")\n", "\n", "tutorial_response = await Runner.run(outline_builder, \"Loops in Java\")\n", "print(tutorial_response.final_output)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from agents import Agent, Runner, handoff, RunContextWrapper\n", "\n", "history_tutor_agent = Agent(\n", "    name=\"<PERSON> Tutor\",\n", "    handoff_description=\"Specialist agent for historical questions\",\n", "    instructions=\"You provide assistance with historical queries. Explain important events and context clearly.\",\n", ")\n", "\n", "math_tutor_agent = Agent(\n", "    name=\"<PERSON>\",\n", "    handoff_description=\"Specialist agent for math questions\",\n", "    instructions=\"You provide assistance with math queries. Explain your reasoning at each step and include examples\"\n", ")\n", "\n", "def on_math_handoff(ctx: RunContextWrapper[None]):\n", "    print(\"Handing off to math tutor agent\")\n", "\n", "def on_history_handoff(ctx: RunContextWrapper[None]):\n", "    print(\"Handing off to history tutor agent\")\n", "\n", "# This agent has the capability to handoff to either the history or math tutor agent\n", "triage_agent = Agent(\n", "    name=\"Triage Agent\",\n", "    instructions=\"You determine which agent to use based on the user's homework question.\" +\n", "    \"If neither agent is relevant, provide a general response.\",\n", "    handoffs=[handoff(history_tutor_agent, on_handoff=on_history_handoff), \n", "              handoff(math_tutor_agent, on_handoff=on_math_handoff)]\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Handing off to math tutor agent\n"]}, {"data": {"text/plain": ["'Adding 2 and 2 is a basic arithmetic operation. Here’s how you do it:\\n\\n1. **Understand the Operation**: Addition is the process of combining two or more numbers to get a total or sum.\\n\\n2. **Line Up the Numbers**: Since these are single-digit numbers, you can simply align them:\\n   \\\\[\\n   2 \\\\\\\\\\n   + 2\\n   \\\\]\\n\\n3. **Add the Numbers**: Start from the right (though here we only have one column):\\n   \\\\[\\n   2 + 2 = 4\\n   \\\\]\\n\\n4. **Write Down the Sum**: The sum of 2 and 2 is 4.\\n\\n**Example**: If you have 2 apples and someone gives you 2 more apples, you count all the apples together to get 4 apples.\\n\\nThis method applies to all basic additions: align the numbers and add starting from the rightmost column.'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["result = await <PERSON>.run(triage_agent, \"How do I add 2 and 2?\")\n", "result.final_output"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Handing off to history tutor agent\n"]}, {"data": {"text/plain": ["'World War II began on September 1, 1939, when Nazi Germany, led by <PERSON>, invaded Poland. This invasion prompted Britain and France to declare war on Germany two days later, on September 3, 1939.\\n\\n### Context Leading to the War:\\n\\n1. **Treaty of Versailles (1919):** After World War I, the Treaty of Versailles placed heavy reparations and territorial losses on Germany. This caused economic hardship and resentment among Germans.\\n\\n2. **Rise of Fascism:** During the 1920s and 1930s, fascist regimes emerged in several countries. <PERSON>'s rise to power in Germany in 1933 was a significant turning point, as he sought to overturn the post-World War I order and expand German territory.\\n\\n3. **Expansion and Appeasement:**\\n   - **Reoccupation of the Rhineland (1936):** Germany reoccupied this demilitarized zone, violating the Treaty of Versailles.\\n   - **Annexation of Austria (1938):** Known as the Anschluss, Austria was annexed into Germany.\\n   - **Munich Agreement (1938):** Britain and France allowed Germany to annex the Sudetenland, part of Czechoslovakia, in hopes of preventing further aggression.\\n\\n4. **Non-Aggression Pact (1939):** Germany and the Soviet Union signed the Molotov-Ribbentrop Pact, pledging not to attack each other. Secretly, they agreed to divide Eastern Europe between them.\\n\\n### Immediate Cause:\\n\\n- **Invasion of Poland:** Germany\\'s attack on Poland was swift, using a strategy known as \"Blitzkrieg\" or lightning war, characterized by fast-moving, coordinated strikes using aircraft, tanks, and troops. The Soviet Union invaded Poland from the east on September 17, 1939, effectively dividing the country.\\n\\nThese actions set off a series of events that escalated into a global conflict involving many nations, eventually drawing in countries such as the United States and the Soviet Union, and lasting until 1945.'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["result = await Runner.run(triage_agent, \"How did WW2 start?\")\n", "result.final_output"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Could you please specify which event or destination you are inquiring about for ticket prices?\n"]}], "source": ["from agents import function_tool\n", "\n", "class ManagerEscalation(BaseModel):\n", "    issue: str # the issue being escalated\n", "    why: str # why can you not handle it? Used for training in the future\n", "\n", "@function_tool\n", "def create_ticket(issue: str):\n", "    \"\"\"\"\n", "    Create a ticket in the system for an issue to be resolved.\n", "    \"\"\"\n", "    print(f\"Creating ticket for issue: {issue}\")\n", "    return \"Ticket created. ID: 12345\"\n", "    # In a real-world scenario, this would interact with a ticketing system\n", "\n", "manager_agent = Agent(\n", "    name=\"Manager\",\n", "    handoff_description=\"Handles escalated issues that require managerial attention\",\n", "    instructions=(\n", "        \"You handle escalated customer issues that the initial custom service agent could not resolve. \"\n", "        \"You will receive the issue and the reason for escalation. If the issue cannot be immediately resolved for the \"\n", "        \"customer, create a ticket in the system and inform the customer.\"\n", "    ),\n", "    tools=[create_ticket],\n", ")\n", "\n", "def on_manager_handoff(ctx: RunContextWrapper[None], input: ManagerEscalation):\n", "    print(\"Escalating to manager agent: \", input.issue)\n", "    print(\"Reason for escalation: \", input.why)\n", "\n", "    # here we might store the escalation in a database or log it for future reference\n", "\n", "customer_service_agent = Agent(\n", "    name=\"Customer Service\",\n", "    instructions=\"You assist customers with general inquiries and basic troubleshooting. \" +\n", "                 \"If the issue cannot be resolved, escalate it to the Manager along with the reason why you cannot fix the issue yourself.\",\n", "    handoffs=[handoff(\n", "        agent=manager_agent,\n", "        input_type=ManagerEscalation,\n", "        on_handoff=on_manager_handoff,\n", "    )]\n", ")\n", "\n", "result = await Runner.run(customer_service_agent, \"Hello how much are tickets?\")\n", "print(result.final_output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Recommended Prompt Prefix\n", "The recommended prompt prefix is a constant that OpenAI recommends you give to handoff agents to improve understanding."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'# System context\\nYou are part of a multi-agent system called the Agents SDK, designed to make agent coordination and execution easy. Agents uses two primary abstraction: **Agents** and **Handoffs**. An agent encompasses instructions and tools and can hand off a conversation to another agent when appropriate. Handoffs are achieved by calling a handoff function, generally named `transfer_to_<agent_name>`. Transfers between agents are handled seamlessly in the background; do not mention or draw attention to these transfers in your conversation with the user.\\n'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from agents.extensions.handoff_prompt import RECOMMENDED_PROMPT_PREFIX\n", "\n", "billing_agent = Agent(\n", "    name=\"Billing agent\",\n", "    instructions=f\"\"\"{RECOMMENDED_PROMPT_PREFIX}\n", "    <Fill in the rest of your prompt here>.\"\"\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}