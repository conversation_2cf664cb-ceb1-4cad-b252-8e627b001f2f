{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Agents\n", "Agents in essence are LLMs that accomplish a specific task. They can be supplemented with tools, structured output, and handoff to other agents."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.0 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["%pip install openai-agents python-dotenv -qU"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "# loads the .env file (if you have a global environment variable, you can skip this)\n", "load_dotenv()\n", "\n", "api_key = os.environ.get(\"OPENAI_API_KEY\")\n", "\n", "if not api_key:\n", "    raise ValueError(\"OPENAI_API_KEY is not set in the environment variables\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'HELLO! I’M DOING GREAT, THANK YOU! HOW ABOUT YOU?'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from agents import Agent, Runner\n", "\n", "agent = Agent(\n", "    name=\"Basic Agent\",\n", "    instructions=\"You are a helpful assistant. Respond on in all caps.\",\n", "    model=\"gpt-4o-mini\"\n", ")\n", "\n", "result = await <PERSON>.run(agent, \"Hello! How are you?\")\n", "result.final_output"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Why was the booger always invited to parties?\\n\\nBecause he was a real \"pick-me-up!\"'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["joke_agent = Agent(\n", "    name=\"Joke Agent\",\n", "    instructions=\"You are a joke teller. You are given a topic and you need to tell a joke about it.\",\n", ")\n", "\n", "topic = \"Boogers\"\n", "result = await Runner.run(joke_agent, topic)\n", "result.final_output"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original joke:\n", "Why did the booger stay in school?\n", "\n", "Because it couldn't stop picking up new things!\n", "\n", "Translated joke:\n", "¿Por qué el moco se quedó en la escuela?\n", "\n", "¡Porque no podía dejar de aprender cosas nuevas!\n"]}], "source": ["language_agent = Agent(\n", "    name=\"Language Agent\",\n", "    instructions=\"You are a language expert. You are given a joke and you need to rewrite it in a different language.\",\n", ")\n", "\n", "joke_result = await Runner.run(joke_agent, topic)\n", "translated_result = await Runner.run(language_agent, f\"Translate this joke to Spanish: {joke_result.final_output}\")\n", "print(f\"Original joke:\\n{joke_result.final_output}\\n\")\n", "print(f\"Translated joke:\\n{translated_result.final_output}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Structured Outputs\n", "Structured outputs are a way to format the output of an LLM in a structured manner. This can be useful for tasks that require specific formatting or data extraction."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Recipe(title='Italian Sausage with Spaghetti', ingredients=['400g spaghetti', '4 Italian sausages, casings removed', '2 tablespoons olive oil', '1 onion, finely chopped', '2 cloves garlic, minced', '1 teaspoon chili flakes (optional)', '1 can (400g) diced tomatoes', '1 tablespoon tomato paste', '1 teaspoon dried oregano', 'Salt and pepper to taste', 'Parmesan cheese, grated (for serving)', 'Fresh basil leaves (for garnish)'], cooking_time=30, servings=4)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel\n", "from agents import Agent\n", "\n", "class Recipe(BaseModel):\n", "    title: str\n", "    ingredients: list[str]\n", "    cooking_time: int # in minutes\n", "    servings: int\n", "\n", "recipe_agent = Agent(\n", "    name=\"Recipe Agent\", \n", "    instructions=(\"You are an agent for creating recipes. You will be given the name of a food and your job\"\n", "                  \" is to output that as an actual detailed recipe. The cooking time should be in minutes.\"),\n", "    output_type=Recipe\n", ")\n", "\n", "response = await Runner.run(recipe_agent, \"Italian Sasuage with Spaghetti\")\n", "recipe = response.final_output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Too<PERSON> Calling\n", "Tool calling is a way to extend the capabilities of an LLM by allowing it to call external tools or APIs. This can be useful for tasks that require access to external data or services."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Getting weather for Dallas\n", "Getting temperature for Dallas\n"]}, {"data": {"text/plain": ["'The weather in Dallas is currently sunny, with a temperature of 70 degrees.'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from agents import Agent, function_tool\n", "\n", "@function_tool\n", "def get_weather(city: str) -> str:\n", "    print(f\"Getting weather for {city}\")\n", "    return \"sunny\"\n", "\n", "@function_tool\n", "def get_temperature(city: str) -> str:\n", "    print(f\"Getting temperature for {city}\")\n", "    return \"70 degrees\"\n", "\n", "agent = Agent(\n", "    name=\"Weather Agent\",\n", "    instructions=\"You are the local weather agent. You are given a city and you need to tell the weather and temperature. For any unrelated queries, say I cant help with that.\",\n", "    tools=[get_weather, get_temperature]\n", ")\n", "\n", "result = await <PERSON>.run(agent, \"Dallas\")\n", "result.final_output"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here are some recent developments in U.S. politics:\n", "\n", "**Democratic Party's Internal Challenges**\n", "\n", "The Democratic Party is facing internal divisions, particularly highlighted during recent budget battles. Some members supported Republican bills, while others opposed them, complicating the party's unified stance against the <PERSON> administration. Calls for new leadership, including critiques of figures like <PERSON>, have emerged. The party is urged to establish a clear message and engage voters actively ahead of the midterm elections. ([ft.com](https://www.ft.com/content/13b4c22d-23e0-4f62-bd5f-5a7afd60e83d?utm_source=openai))\n", "\n", "**<PERSON>'s Influence on Cultural Institutions**\n", "\n", "Former President <PERSON> continues to assert influence over American institutions. As chair of the John F<PERSON> Kennedy Center for the Performing Arts, he aims to remove \"woke\" influences and discourage performances he deems \"anti-American.\" This cultural campaign parallels actions in academia, particularly targeting Ivy League schools like Columbia University, which recently lost $400 million in federal grants over handling of Jewish student harassment. <PERSON>'s efforts appear to be a broader attack on elite institutions and liberal influences in America. ([ft.com](https://www.ft.com/content/7bbb477a-0bf4-4222-9e28-817c2ae996ec?utm_source=openai))\n", "\n", "**Legal Proceedings Involving Georgetown Scholar**\n", "\n", "A federal judge in Alexandria, Virginia, has ordered that Georgetown scholar <PERSON><PERSON> cannot be deported until a court decision is made. <PERSON><PERSON> was detained by the Trump Administration, accused of spreading Hamas propaganda due to his social media posts and his wife's pro-Palestinian sentiments. His attorney argues that his detention violates his free speech and due process rights, noting that he holds a valid visa and has no criminal record. Georgetown University supports <PERSON><PERSON>'s right to free speech and expects a fair legal process. ([apnews.com](https://apnews.com/article/21fc205cebbbbba2ed260050df04702a?utm_source=openai))\n", "\n", "**Debate Over Misinformation and Information Fragmentation**\n", "\n", "An article argues that the focus on misinformation distracts from a more significant issue: the fragmentation of information sources leading to different groups not sharing the same information, regardless of its truthfulness. This fragmentation has led to information bubbles and political polarization, contributing to democratic dysfunction and the widening ideological divide, especially among younger generations. The author stresses that this division in information consumption contributes to democratic dysfunction and the widening ideological divide, especially among younger generations. ([ft.com](https://www.ft.com/content/bbc80e1c-60a7-4f3d-a9a1-a4e68cf36912?utm_source=openai))\n", "\n", "\n", "## Recent Developments in U.S. Politics:\n", "- [Transcript: Swamp Notes - Why Democrats can't get their act together](https://www.ft.com/content/13b4c22d-23e0-4f62-bd5f-5a7afd60e83d?utm_source=openai)\n", "- [<PERSON> takes on America's elite](https://www.ft.com/content/7bbb477a-0bf4-4222-9e28-817c2ae996ec?utm_source=openai)\n", "- [US government cannot deport Georgetown scholar until court rules, judge orders](https://apnews.com/article/21fc205cebbbbba2ed260050df04702a?utm_source=openai) \n"]}], "source": ["from agents import WebSearchTool\n", "\n", "news_agent = Agent(\n", "    name=\"News Reporter\",\n", "    instructions=\"You are a news reporter. Your job is to find recent news articles on the internet about US politics.\",\n", "    tools=[WebSearchTool()]\n", ")\n", "\n", "result = await Runner.run(news_agent, \"find news\")\n", "print(result.final_output)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}